<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // 1. Add egypt_region_id to customer_addresses table
        if (!Schema::hasColumn('customer_addresses', 'egypt_region_id')) {
            Schema::table('customer_addresses', function (Blueprint $table) {
                $table->foreignId('egypt_region_id')->nullable()->after('country_id')->constrained()->onDelete('cascade');
            });
        }

        // 2. Add egypt_region_id to orders table
        if (!Schema::hasColumn('orders', 'egypt_region_id')) {
            Schema::table('orders', function (Blueprint $table) {
                $table->foreignId('egypt_region_id')->nullable()->after('country_id')->constrained()->onDelete('cascade');
            });
        }

        // 3. Modify shipping_charges table
        // Clear existing data first
        DB::table('shipping_charges')->truncate();
        
        Schema::table('shipping_charges', function (Blueprint $table) {
            // Check if country_id exists and drop it
            if (Schema::hasColumn('shipping_charges', 'country_id')) {
                $table->dropForeign(['country_id']);
                $table->dropColumn('country_id');
            }
            
            // Check if egypt_region_id doesn't exist and add it
            if (!Schema::hasColumn('shipping_charges', 'egypt_region_id')) {
                $table->foreignId('egypt_region_id')->after('id')->constrained()->onDelete('cascade');
            }
        });

        // 4. Seed shipping charges for Egypt regions
        $this->seedShippingCharges();
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Remove egypt_region_id from customer_addresses
        if (Schema::hasColumn('customer_addresses', 'egypt_region_id')) {
            Schema::table('customer_addresses', function (Blueprint $table) {
                $table->dropForeign(['egypt_region_id']);
                $table->dropColumn('egypt_region_id');
            });
        }

        // Remove egypt_region_id from orders
        if (Schema::hasColumn('orders', 'egypt_region_id')) {
            Schema::table('orders', function (Blueprint $table) {
                $table->dropForeign(['egypt_region_id']);
                $table->dropColumn('egypt_region_id');
            });
        }

        // Restore shipping_charges table
        Schema::table('shipping_charges', function (Blueprint $table) {
            // Check if egypt_region_id exists and drop it
            if (Schema::hasColumn('shipping_charges', 'egypt_region_id')) {
                $table->dropForeign(['egypt_region_id']);
                $table->dropColumn('egypt_region_id');
            }
            
            // Check if country_id doesn't exist and add it back
            if (!Schema::hasColumn('shipping_charges', 'country_id')) {
                $table->foreignId('country_id')->after('id')->constrained()->onDelete('cascade');
            }
        });
    }

    /**
     * Seed shipping charges for Egypt regions.
     */
    private function seedShippingCharges(): void
    {
        // Get all Egypt regions
        $regions = DB::table('egypt_regions')->get();
        
        $shippingCharges = [];
        
        foreach ($regions as $region) {
            // Set different shipping rates based on region
            $amount = match($region->code) {
                'cairo', 'giza', 'qalyubia' => 30, // Greater Cairo area - lower shipping
                'alex', 'beheira', 'damietta' => 40, // Northern regions
                'port-said', 'suez', 'ismailia' => 45, // Canal cities
                'dakahlia', 'sharqia', 'gharbia', 'kafr-sheikh', 'menoufia' => 50, // Delta regions
                'fayoum', 'beni-suef' => 55, // Middle Egypt
                'minya', 'asuit', 'sohag', 'qena' => 60, // Upper Egypt
                'luxor', 'aswan' => 70, // Far South
                'red-sea', 'south-sinai', 'north-sinai', 'matrouh', 'new-valley' => 80, // Remote areas
                default => 50 // Default rate
            };
            
            $shippingCharges[] = [
                'egypt_region_id' => $region->id,
                'amount' => $amount,
                'created_at' => now(),
                'updated_at' => now(),
            ];
        }
        
        DB::table('shipping_charges')->insert($shippingCharges);
    }
};
