<?php

return [
    'billing_address'=> 'Billing Address',
    'name_full' => 'Full Name',
    'email' => 'Email',
    'phone' => 'Phone Number',
    'address_1' => 'Address 1',
    'address_2' => 'Address 2',
    'optional' => 'Optional',
    'country' => 'Country',
    'city' => 'City',
    'state' => 'State',
    'zip' => 'ZIP Code',
    'note_order' => 'Note',
    'order_total' => 'Order Total',
    'products' => 'Products',
    'no_products' => 'There are no products in your cart.',
    'subtotal' => 'Subtotal',
    'shipping' => 'Shipping',
    'grand_total' => 'Grand Total',
    'payment_method' => 'Payment Method',
    'payment_method_cod' => 'Cash on Delivery',
    'payment_method_paypal' => 'Pay with PayPal',
    'payment_method_stripe' => 'Pay with Stripe',
    'test_mode_message' => 'Please note that test mode is enabled. No money will be deducted from your account.',
    'test_mode' => 'Test Mode',
    'place_order' => 'Place Order',
    'processing' => 'Processing...',
    'order_placed' => 'Order Placed Successfully',
    'order_placed_message' => 'Your order has been placed successfully. Order details will be sent to your email.',
    'order_failed' => 'Order Failed',
    'order_id' => 'Order ID',
    'order_date' => 'Order Date',
    'order_status' => 'Order Status',
    'notes' => 'Note: Your order will be delivered within 4 days.',
    'total' => 'Total: :amount EGP',
    'invoice_sent' => 'Invoice details have been sent to your email.',
    'continue_shopping' => 'Continue Shopping',
    'thank_you_for_your_order' => 'Thank You for Your Order!',
    'order_details_are_as_follows:' => 'We appreciate your business. Your order details are as follows:',
    'invalid_coupon' => 'Invalid or expired coupon code',
    'min_order_amount' => 'Minimum order amount for this coupon: :amount EGP',
    'enter_coupon_code' => 'Please enter a coupon code',
    'coupon_applied_success' => 'Coupon applied successfully',
    'coupon_removed' => 'Coupon removed successfully',
    'coupon_placeholder' => 'Enter coupon code',
    'remove' => 'Remove',
    'discount' => 'Discount',
    'apply_coupon' => 'Apply Coupon',
    'coupon_code' => 'Coupon Code',
    'select_region_first' => 'Please select your region first.',
];
