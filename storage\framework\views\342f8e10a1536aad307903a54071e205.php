<?php $__env->startSection('TitlePage' , 'Shop'); ?>
<?php $__env->startSection('content'); ?>

<!-- Breadcrumb Start -->
<div class="container-fluid">
    <div class="row px-xl-5">
        <div class="col-12">
            <nav class="breadcrumb bg-light mb-30">
                <a class="breadcrumb-item text-dark" href="#"><?php echo e(__('breadcrumb.home')); ?></a>
                <a class="breadcrumb-item text-dark" href="#"><?php echo e(__('breadcrumb.shop')); ?></a>
                <span class="breadcrumb-item active"><?php echo e(__('breadcrumb.shop_list')); ?></span>
            </nav>
        </div>
    </div>
</div>
<!-- Breadcrumb End -->


<!-- Shop Start -->
<div class="container-fluid">
    <div class="row px-xl-5">
        <!-- Shop Sidebar Start -->
            <!-- category Start -->

        <div class="col-lg-3 col-md-4 order-2 order-md-1">
            <h5 class="section-title position-relative text-uppercase mb-3">
                <span class="bg-secondary pr-3"><?php echo e(__('shop.filter_by_category')); ?></span>
            </h5>
            <div class="bg-light p-3 mb-30">
                <?php if($categories->isNotEmpty()): ?>
                    <div class="accordion accordion-flush" id="accordionExample">
                        <?php $__currentLoopData = $categories; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $category): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <div class="accordion-item">
                                <?php if(isset($category->sub_category) && $category->sub_category->isNotEmpty()): ?>
                                    <h2 class="accordion-header" id="heading<?php echo e($category->id); ?>">
                                        <button class="accordion-button collapsed d-flex justify-content-between align-items-center" type="button" data-bs-toggle="collapse" data-bs-target="#collapse<?php echo e($category->id); ?>" aria-expanded="false" aria-controls="collapse<?php echo e($category->id); ?>">
                                            <?php echo e($category->name); ?>

                                            <span class="badge border font-weight-normal text-muted"><?php echo e($category->products_count); ?></span>
                                        </button>
                                    </h2>
                                    <div id="collapse<?php echo e($category->id); ?>" class="accordion-collapse collapse" aria-labelledby="heading<?php echo e($category->id); ?>" data-bs-parent="#accordionExample">
                                        <div class="accordion-body">
                                            <div class="navbar-nav">
                                                <?php $__currentLoopData = $category->sub_category; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $sub_category): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                    <a href="<?php echo e(route('products.index', ['category' => $sub_category->id])); ?>" class="nav-item nav-link">
                                                        <?php echo e($sub_category->name); ?>

                                                        <span class="badge border font-weight-normal text-muted"><?php echo e($sub_category->products_count); ?></span>
                                                    </a>
                                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                            </div>
                                        </div>
                                    </div>
                                <?php else: ?>
                                <a onclick="window.location.href='<?php echo e(route('website.shop', $category->slug)); ?>'"
                                    style="cursor: pointer;"
                                    class="nav-item nav-link <?php echo e(request()->is('*/shop/'.$category->slug) ? 'text-primary' : 'text-muted'); ?> d-flex justify-content-between align-items-center text-primary">
                                        <?php echo e($category->name); ?>

                                    <span class="badge border font-weight-normal text-muted"><?php echo e($category->products_count); ?></span>
                                </a>



                                    <?php endif; ?>
                                </div>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                <a class="nav-item nav-link text-muted d-flex  align-items-center text-primary" href="<?php echo e(route('website.shop')); ?>"><i class="fa-solid fa-filter-circle-xmark"></i>  <?php echo e(__('shop.remove_filter')); ?></a>
                    </div>
                <?php endif; ?>
            </div>
                <!-- category End -->

            <!-- brand Start -->
            <?php if($brands->isNotEmpty()): ?>
            <h5 class="section-title position-relative text-uppercase mb-3"><span class="bg-secondary pr-3"><?php echo e(__('shop.filter_by_brand')); ?></span></h5>
            <div class="bg-light p-4 mb-30">
                <div class="d-flex align-items-center justify-content-between mb-3">
                    <label><?php echo e(__('shop.all_brands')); ?></label>
                    <span class="badge border font-weight-normal text-muted"><?php echo e($brandCount); ?></span>
                </div>
                <?php $__currentLoopData = $brands; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $brand): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <div class="custom-control custom-checkbox d-flex align-items-center justify-content-between mb-3">
                    <input <?php echo e((in_array($brand->id,$brandsArray)) ? 'checked' : ''); ?> type="checkbox" name="brand[]" value="<?php echo e($brand->id); ?>" class="custom-control-input brand-label" id="brand-<?php echo e($brand->id); ?>">
                    <label class="custom-control-label" for="brand-<?php echo e($brand->id); ?>"  style="cursor: pointer;">
                        <?php echo e($brand->name); ?>

                    </label>
                    <span class="badge border font-weight-normal text-muted"><?php echo e($brand->products_count); ?></span>
                </div>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>

            </div>
            <?php endif; ?>
            <!-- brand End -->

        <!-- Price Start -->
            <h5 class="section-title position-relative text-uppercase mb-3"><span class="bg-secondary pr-3"><?php echo e(__('shop.filter_by_price')); ?></span></h5>
            <div class="bg-light p-4 mb-30">
                <input type="text" class="js-range-slider" name="my_range" value="" />
            </div>

        </div>
        <!-- Shop Sidebar End -->

        <!-- Shop Product Start -->
        <div class="col-lg-9 col-md-8 order-1 order-md-2">
            <div class="row pb-3">
                <div class="col-12 pb-1">
                    <div class="d-flex align-items-center justify-content-between mb-4">
                        <div>
                            <a class="btn btn-sm btn-light mr-2" href="<?php echo e(route('website.shop')); ?>"><i class="fa-solid fa-filter-circle-xmark"></i></a>
                            <button class="btn btn-sm btn-light"><i class="fa fa-th-large"></i></button>
                        </div>
                        <div class="ml-2">
                            <div class="btn-group">
                                <select name="sort" id="sort" class="form-control btn btn-sm btn-light">
                                    <option value="latest" <?php echo e(($sort == 'latest') ? 'selected' : ''); ?>><?php echo e(__('shop.sort_by')); ?> : <?php echo e(__('shop.Latest')); ?></option>
                                    <option value="price_desc" <?php echo e(($sort == 'price_desc') ? 'selected' : ''); ?>><?php echo e(__('shop.sort_by')); ?> : <?php echo e(__('shop.price_highest')); ?></option>
                                    <option value="price_asc" <?php echo e(($sort == 'price_asc') ? 'selected' : ''); ?>><?php echo e(__('shop.sort_by')); ?> : <?php echo e(__('shop.price_lowest')); ?></option>
                                </select>
                            </div>
                            
                            <div class="btn-group ml-2">
                                <button type="button" class="btn btn-sm btn-light dropdown-toggle"
                                    data-toggle="dropdown"><?php echo e(__('shop.Showing')); ?></button>
                                <div class="dropdown-menu dropdown-menu-right">
                                    <a class="dropdown-item" href="#">10</a>
                                    <a class="dropdown-item" href="#">20</a>
                                    <a class="dropdown-item" href="#">30</a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <?php if($products->isNotEmpty()): ?>
                    <?php $__currentLoopData = $products; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $product): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <div class="col-lg-4 col-md-6 col-sm-6 pb-1">
                            <div class="product-item bg-light mb-4">
                                <div class="product-img position-relative overflow-hidden">
                                    <img class="img-fluid image-Custom" src="<?php echo e(\App\Helpers\ImageHelper::getProductImageUrl($product)); ?>"
                                        alt="<?php echo e($product->name); ?>" style="height: 250px; width: 100%;"
                                        onerror="this.src='<?php echo e(asset('admin/assets/img/product/noimage.png')); ?>'">
                                    <?php if($product->selling_price < $product->price): ?>
                                    <div class="badge bg-danger text-white position-absolute" style="top: 10px; left: 10px; z-index: 1; padding: 5px 10px; border-radius: 3px;">
                                        <?php echo e(round((($product->price - $product->selling_price) / $product->price) * 100)); ?>% <?php echo e(__('product.discount')); ?>

                                    </div>
                                    <?php endif; ?>
                                    <div class="product-action">
                                        <?php if($product->qty >= $product->minqty): ?>
                                        <a class="btn btn-outline-dark btn-square add-to-cart" data-toggle="tooltip" title="<?php echo e(__('product.add_to_cart')); ?>"
                                        data-product-id="<?php echo e($product->id); ?>"
                                        href="javascript:void(0);">
                                        <i class="fa fa-cart-plus"></i></a>
                                        <?php else: ?>
                                        <a class="btn btn-outline-dark btn-square" data-toggle="tooltip" title="<?php echo e(__('product.unavailable')); ?>"><i class="fa-solid fa-store-slash"></i></a>
                                        <?php endif; ?>
                                        <a class="btn btn-outline-dark btn-square" onclick="addToWishlist(<?php echo e($product->id); ?>)" href="javascript:void(0);" data-toggle="tooltip" title="<?php echo e(__('product.add_wishlist')); ?>"><i class="far fa-heart"></i></a>
                                        <a class="btn btn-outline-dark btn-square" href="<?php echo e(route('get_product_slug', [$product->category->slug, $product->slug])); ?>" data-toggle="tooltip" title="<?php echo e(__('product.view_deatils')); ?>"><i class="fa-solid fa-eye"></i></a>
                                    </div>
                                </div>
                                <div class="text-center py-4">
                                    <a class="h6 text-decoration-none" href="<?php echo e(route('get_product_slug', [$product->category->slug, $product->slug])); ?>" style="display: block; height: 40px; overflow: hidden;"><?php echo e($product->name); ?></a>
                                    <div class="d-flex align-items-center justify-content-center mt-1">
                                        <span class="text-muted small">
                                            <a href="<?php echo e(route('website.category_slug', $product->category->slug)); ?>" class="text-muted"><?php echo e($product->category->name); ?></a>
                                            <?php if($product->brand): ?>
                                             | <a href="<?php echo e(route('website.shop')); ?>?brand=<?php echo e($product->brand_id); ?>" class="text-muted"><?php echo e($product->brand->name); ?></a>
                                            <?php endif; ?>
                                        </span>
                                    </div>
                                    <div class="d-flex align-items-center justify-content-center mt-2">
                                        <h5><?php echo e($product->selling_price); ?> <?php echo e(__('product.egp')); ?></h5>
                                        <?php if($product->selling_price < $product->price): ?>
                                        <h6 class="text-muted ml-2"><del><?php echo e($product->price); ?> <?php echo e(__('product.egp')); ?></del></h6>
                                        <?php endif; ?>
                                    </div>
                                    <div class="d-flex align-items-center justify-content-center mb-1">
                                        <div class="back-stars">
                                            <small class="fa fa-star"></small>
                                            <small class="fa fa-star"></small>
                                            <small class="fa fa-star"></small>
                                            <small class="fa fa-star"></small>
                                            <small class="fa fa-star"></small>
                                            <div class="front-stars" style="width: <?php echo e($product->avgRatingPer); ?>%">
                                                <small class="fa fa-star"></small>
                                                <small class="fa fa-star"></small>
                                                <small class="fa fa-star"></small>
                                                <small class="fa fa-star"></small>
                                                <small class="fa fa-star"></small>
                                            </div>
                                        </div>
                                        <small class="pt-1"> (<?php echo e($product->product_ratings_count); ?>)</small>
                                    </div>
                                    <div class="d-flex align-items-center justify-content-center mt-2">
                                        <a href="<?php echo e(route('get_product_slug', [$product->category->slug, $product->slug])); ?>" class="btn btn-primary"><?php echo e(__('main.show_details')); ?> <i class="fa-solid fa-arrow-right"></i></a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                <?php else: ?>
                    <div class="col-12 text-center py-5">
                        <h4 class="text-muted"><?php echo e(__('shop.search')); ?></h4>
                        <h5 class="text-muted">
                            <?php echo e(__('shop.contact_line')); ?>

                            <a href="<?php echo e(url('page/contact-us')); ?>" target="_blank" class="text-primary">
                                <?php echo e(__('shop.here')); ?> <i class="fa-solid fa-arrow-up-right-from-square" style="font-size: 0.8rem;"></i>
                            </a>
                            <?php echo e(__('shop.or_hotline')); ?>

                        </h5>

                    </div>
                <?php endif; ?>

                <div class="col-12">
                    <nav>
                        <ul class="pagination justify-content-center">
                            <?php echo e($products->withQueryString()->links()); ?>

                        </ul>
                    </nav>
                </div>
            </div>
            <!-- Shop Product End -->
        </div>
    </div>
</div>
    <!-- Shop End -->

<?php $__env->stopSection(); ?>

<?php $__env->startSection('customjs'); ?>
<script>
    $.ajaxSetup({
        headers: {
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        }
    });




    // Initialize tooltips
    $('[data-toggle="tooltip"]').tooltip();

    rangeSlider = $(".js-range-slider").ionRangeSlider({
    type: 'double',
    min: 100,
    max: <?php echo e($price); ?>,
    from: <?php echo e($priceMin); ?> ,
    step: 100,
    to: <?php echo e($priceMax); ?>,
    skin: "round",
    prettify_enabled: true,
    prettify_separator: ",",
    max_postfix: "+",
    prefix: "EGP ",
    grid: true,
    grid_num: 3,
    onFinish: function(){
        apply_filters()
    }
    });

    var slider = $(".js-range-slider").data('ionRangeSlider');

    $(".brand-label").change(function() {
        apply_filters();
    });

    $("#sort").change(function() {
        apply_filters();
    });

    // Add to cart functionality
    $(document).on('click', '.add-to-cart', function(e) {
        e.preventDefault();
        var productId = $(this).data('product-id');

        $.ajax({
            method: 'POST',
            url: "<?php echo e(route('product.addToCart')); ?>",
            data: {
                _token: "<?php echo e(csrf_token()); ?>",
                product_id: productId,
                quantity: 1
            },
            success: function(response) {
                Swal.fire({
                    icon: response.icon,
                    text: response.msg,
                    timer: 2000,
                    timerProgressBar: true,
                });
                updateCartCount();
            },
            error: function(xhr, status, error) {
                console.error('Error: ' + error);
                console.error(xhr.responseText);
            }
        });
    });

    // Add to wishlist functionality
    function addToWishlist(productId) {
        $.ajax({
            type: "POST",
            url: "<?php echo e(route('website.addToWishlist')); ?>",
            data: {
                product_id: productId,
                _token: "<?php echo e(csrf_token()); ?>"
            },
            success: function(response) {
                if (response.status) {
                    Swal.fire({
                        icon: 'success',
                        title: response.message,
                        showConfirmButton: false,
                        timer: 1500
                    });
                    updateWishlistCount();
                } else {
                    Swal.fire({
                        icon: 'error',
                        title: response.message,
                        showConfirmButton: false,
                        timer: 1500
                    });
                }
            },
            error: function(xhr) {
                if (xhr.status === 401) {
                    window.location.href = "<?php echo e(route('login')); ?>";
                }
            }
        });
    }

    // Update cart count
    function updateCartCount() {
        $.ajax({
            url: "<?php echo e(route('cart.count')); ?>",
            type: "GET",
            success: function(response) {
                $('#cart-count').text(response.count);
            }
        });
    }

    // Update wishlist count
    function updateWishlistCount() {
        $.ajax({
            url: "<?php echo e(route('wishlist.count')); ?>",
            type: "GET",
            success: function(response) {
                $('#wishlist-count').text(response.count);
            }
        });
    }

    function apply_filters() {
        var brands = [];
        $(".brand-label").each(function() {
            if ($(this).is(":checked") == true) {
                brands.push($(this).val());
            }
        });

        var url ='<?php echo e(url()->current()); ?>?';
        // price
        var priceMin = slider.result.from;
        if (priceMin < 10) {
            priceMin = 10;  // تأكد من أن السعر الأدنى دائمًا 10
        }
        url += '&price_min='+slider.result.from+'&price_max='+slider.result.to;

        // brands
        if (brands.length > 0) {
            url+= '&brand='+brands.toString();
        }

        var keyword = $('#search').val();
        if (keyword.length > 0) {
            url += '&search='+keyword;
        }

        // sort
        url += '&sort='+$("#sort").val();
        window.location.href = url;
    }
</script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('website.layouts.master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\laragon\www\ecommerce-partscars\resources\views/website/shop.blade.php ENDPATH**/ ?>