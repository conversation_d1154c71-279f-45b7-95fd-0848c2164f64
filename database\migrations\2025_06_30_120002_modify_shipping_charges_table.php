<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('shipping_charges', function (Blueprint $table) {
            // Drop the foreign key constraint first
            $table->dropForeign(['country_id']);
            // Drop the country_id column
            $table->dropColumn('country_id');
            // Add the new egypt_region_id column
            $table->foreignId('egypt_region_id')->after('id')->constrained()->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('shipping_charges', function (Blueprint $table) {
            // Drop the foreign key constraint first
            $table->dropForeign(['egypt_region_id']);
            // Drop the egypt_region_id column
            $table->dropColumn('egypt_region_id');
            // Add back the country_id column
            $table->foreignId('country_id')->after('id')->constrained()->onDelete('cascade');
        });
    }
};
