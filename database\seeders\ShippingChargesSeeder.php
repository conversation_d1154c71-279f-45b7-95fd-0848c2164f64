<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use App\Models\EgyptRegion;

class ShippingChargesSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get all Egypt regions
        $regions = EgyptRegion::all();
        
        $shippingCharges = [];
        
        foreach ($regions as $region) {
            // Set different shipping rates based on region
            $amount = match($region->code) {
                'cairo', 'giza', 'qalyubia' => 30, // Greater Cairo area - lower shipping
                'alex', 'beheira', 'damietta' => 40, // Northern regions
                'port-said', 'suez', 'ismailia' => 45, // Canal cities
                'dakahlia', 'sharqia', 'gharbia', 'kafr-sheikh', 'menoufia' => 50, // Delta regions
                'fayoum', 'beni-suef' => 55, // Middle Egypt
                'minya', 'asuit', 'sohag', 'qena' => 60, // Upper Egypt
                'luxor', 'aswan' => 70, // Far South
                'red-sea', 'south-sinai', 'north-sinai', 'matrouh', 'new-valley' => 80, // Remote areas
                default => 50 // Default rate
            };
            
            $shippingCharges[] = [
                'egypt_region_id' => $region->id,
                'amount' => $amount,
                'created_at' => now(),
                'updated_at' => now(),
            ];
        }
        
        DB::table('shipping_charges')->insert($shippingCharges);
    }
}
