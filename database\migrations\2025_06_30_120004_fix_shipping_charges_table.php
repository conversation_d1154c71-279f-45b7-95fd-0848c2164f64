<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Clear existing data first
        DB::table('shipping_charges')->truncate();
        
        Schema::table('shipping_charges', function (Blueprint $table) {
            // Check if country_id exists and drop it
            if (Schema::hasColumn('shipping_charges', 'country_id')) {
                $table->dropForeign(['country_id']);
                $table->dropColumn('country_id');
            }
            
            // Check if egypt_region_id doesn't exist and add it
            if (!Schema::hasColumn('shipping_charges', 'egypt_region_id')) {
                $table->foreignId('egypt_region_id')->after('id')->constrained()->onDelete('cascade');
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('shipping_charges', function (Blueprint $table) {
            // Check if egypt_region_id exists and drop it
            if (Schema::hasColumn('shipping_charges', 'egypt_region_id')) {
                $table->dropForeign(['egypt_region_id']);
                $table->dropColumn('egypt_region_id');
            }
            
            // Check if country_id doesn't exist and add it back
            if (!Schema::hasColumn('shipping_charges', 'country_id')) {
                $table->foreignId('country_id')->after('id')->constrained()->onDelete('cascade');
            }
        });
    }
};
