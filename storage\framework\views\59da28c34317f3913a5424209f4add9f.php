<?php $__env->startSection('TitlePage', 'create shipping'); ?>
<?php $__env->startSection('content'); ?>
<div class="content">
    <div class="page-header">
        <div class="page-title">
            <h4>Shipping Add</h4>
            <h6>Create new Shipping</h6>
        </div>
    </div>
    
    <div class="card">
        <div class="card-body">
            <form action="<?php echo e(route('shipping.store')); ?>" method="POST" name="shippingForm" id="shippingForm"
                enctype="multipart/form-data">
                <?php echo csrf_field(); ?>
                <div class="row">
                    <div class="col-lg-4 col-sm-6 col-12">
                        <div class="form-group">
                            <select class="select" name="egypt_region" id="egypt_region">
                                <option value="">Select Egypt Region</option>
                                <?php if($egyptRegions->isNotEmpty()): ?>
                                <?php $__currentLoopData = $egyptRegions; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $region): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <option value="<?php echo e($region->id); ?>">
                                    <?php echo e($region->name); ?> - <?php echo e($region->name_ar); ?>

                                </option>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                <?php endif; ?>
                            </select>
                            <p></p>
                        </div>
                    </div>
                    <div class="col-lg-4 col-sm-6 col-12">
                        <div class="form-group">
                            <input type="text" name="amount" id="amount" class="form-control" placeholder="Amount Per Order"
                                value="<?php echo e(old('amount')); ?>">
                                <p></p>
                            </div>
                        </div>
                    <div class="col-lg-4">
                        <button type="submit" class="btn btn-sm btn-submit">create</button>
                    </div>
                </div>
            </form>
        </div>
    </div>
    


    <div class="card">
        <div class="card-body">
            <div class="table-top">
                <div class="search-set">
                    <div class="search-input">
                        <a class="btn btn-searchset"><img src="<?php echo e(asset('admin/assets/img/icons/search-white.svg')); ?>"
                                alt="img"></a>
                    </div>
                </div>
                <div class="wordset">
                    <ul>
                        <li>
                            <a data-bs-toggle="tooltip" data-bs-placement="top" title="pdf"><img
                                    src="<?php echo e(asset('admin/assets/img/icons/pdf.svg')); ?>" alt="img"></a>
                        </li>
                        <li>
                            <a data-bs-toggle="tooltip" data-bs-placement="top" title="excel"><img
                                    src="<?php echo e(asset('admin/assets/img/icons/excel.svg')); ?>" alt="img"></a>
                        </li>
                        <li>
                            <a data-bs-toggle="tooltip" data-bs-placement="top" title="print"><img
                                    src="<?php echo e(asset('admin/assets/img/icons/printer.svg')); ?>" alt="img"></a>
                        </li>
                    </ul>
                </div>
            </div>

            <div class="table-responsive">
                <table class="table datanew">
                    <thead>
                        <tr>
                            <th>
                                <label class="checkboxs">
                                    <input type="checkbox" id="select-all">
                                    <span class="checkmarks"></span>
                                </label>
                            </th>
                            <th>#</th>
                            
                            <th>Region</th>
                            <th>Amount Per Order</th>
                            <th>Action</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php $__empty_1 = true; $__currentLoopData = $shippingCharges; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $shippingCharge): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                        <tr>
                            <td>
                                <label class="checkboxs">
                                    <input type="checkbox">
                                    <span class="checkmarks"></span>
                                </label>
                            </td>

                            <td><?php echo e($loop->iteration); ?></td>
                            <td><?php echo e($shippingCharge->name); ?> - <?php echo e($shippingCharge->name_ar); ?></td>
                            <td><?php echo e($shippingCharge->amount); ?> EGP</td>

                            <td>
                                <a class="me-3" href="<?php echo e(route('shipping.edit',$shippingCharge->id)); ?>">
                                    <img src="<?php echo e(asset('admin/assets/img/icons/edit.svg')); ?>" alt="img">
                                </a>
                                <a class="me-3" href="javascript:void(0);" onclick="deleteRecord(<?php echo e($shippingCharge->id); ?>)">
                                    <img src="<?php echo e(asset('admin/assets/img/icons/delete.svg')); ?>" alt="img">
                                </a>
                            </td>
                        </tr>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                        <tr>
                            <td colspan="5" class="text-center">No Shipping Charges Yet!</td>
                        </tr>
                        <?php endif; ?>

                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<script>
    $(document).ready(function() {
    $("#shippingForm").submit(function(event) {
        event.preventDefault();
        $('button[type="submit"]').prop('disabled', true);

        $.ajax({
            headers: {
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
            },
            url: '<?php echo e(route('shipping.store')); ?>',
            type: 'post',
            data: $(this).serialize(),
            dataType: 'json',
            success: function(response) {
                $('button[type="submit"]').prop('disabled', false);

                if (response.status === false) {
                    handleErrors(response.errors);
                } else {
                    // حفظ ناجح، إعادة التوجيه إلى صفحة الشكر
                    window.location.href = "<?php echo e(route('shipping.create')); ?>";
                }
            },
            error: function(xhr, status, error) {
                $('button[type="submit"]').prop('disabled', false);
                // يمكن معالجة الأخطاء الأخرى هنا
            }
        });
    });

    function handleErrors(errors) {
        var fields = ['country', 'amount'];

        fields.forEach(function(field) {
            var input = $("#" + field);
            var errorFeedback = input.siblings('p');
            if (errors[field]) {
                input.addClass('is-invalid');
                errorFeedback
                .addClass('invalid-feedback')
                .html(errors[field][0]);
            } else {
                input.removeClass('is-invalid');
                errorFeedback
                .removeClass('invalid-feedback')
                .html('');
            }
        });
    }
    });

    function deleteRecord(id) {
        if(confirm("Are you sure you want to delete this record?")) {
            var url = '<?php echo e(route('shipping.destroy', 'id')); ?>';
            var newUrl = url.replace("id", id);

            $.ajax({
                headers: {
                    'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                },
                url: newUrl,
                type: 'DELETE',
                dataType: 'json',
                success: function(response) {
                    if(response.status === true) {
                        window.location.href = "<?php echo e(route('shipping.create')); ?>";
                    }
                },
                error: function(xhr, status, error) {
                    alert("An error occurred while deleting the record. Please try again.");
                }
            });
        }
    }


</script>

<?php $__env->stopSection(); ?>

<?php echo $__env->make('admin.layouts.master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\laragon\www\ecommerce-partscars\resources\views/admin/shipping/create.blade.php ENDPATH**/ ?>