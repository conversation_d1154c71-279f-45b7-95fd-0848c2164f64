<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\EgyptRegion;
use App\Models\ShippingCharges;
use Illuminate\Support\Facades\Validator;
use Illuminate\Http\Request;

class ShippingController extends Controller
{
    public function create(){
        $egyptRegions = EgyptRegion::get();
        $shippingCharges = ShippingCharges::select('shipping_charges.*','egypt_regions.name', 'egypt_regions.name_ar')
                            ->leftjoin('egypt_regions','egypt_regions.id','shipping_charges.egypt_region_id')->get();
        return view('admin.shipping.create' ,compact('egyptRegions' , 'shippingCharges'));
    }

    public function store(Request $request) {
        $validator = Validator::make($request->all(), [
            'egypt_region' => 'required',
            'amount' => 'required|numeric',
        ]);

        if ($validator->passes()) {
            // check if shipping already added for this region
            $count = ShippingCharges::where('egypt_region_id',$request->egypt_region)->count();
            if ($count > 0 ){
                session()->flash('error', 'Shipping Already added for this region');
                return response()->json([
                    'status' => true,
                ]);
            }

            $shipping = new ShippingCharges();
            $shipping->egypt_region_id = $request->egypt_region;
            $shipping->amount = $request->amount;
            $shipping->save();

            session()->flash('success', 'Shipping added successfully');
            return response()->json([
                'status' => true,
            ]);

        } else {
            return response()->json([
                'status' => false,
                'errors' => $validator->errors()
            ]);
        }
    }

    public function edit($id){
        $shippingCharge = ShippingCharges::find($id);
        $egyptRegions = EgyptRegion::get();
        return view('admin.shipping.edit' ,compact('shippingCharge','egyptRegions'));
    }

    public function update($id ,Request $request) {
        $shipping = ShippingCharges::find($id);

        $validator = Validator::make($request->all(), [
            'egypt_region' => 'required',
            'amount' => 'required|numeric',
        ]);

        if ($validator->passes()) {

            if ($shipping == null ){
                session()->flash('error', 'Shipping Not Found');
                return response()->json([
                    'status' => true,
                ]);
            }

            $shipping = ShippingCharges::find($id);
            $shipping->egypt_region_id = $request->egypt_region;
            $shipping->amount = $request->amount;
            $shipping->save();

            session()->flash('success', 'Shipping updated successfully');
            return response()->json([
                'status' => true,
            ]);

        } else {
            return response()->json([
                'status' => false,
                'errors' => $validator->errors()
            ]);
        }
    }

    public function destroy($id) {
        $shippingCharge = ShippingCharges::find($id);
        
        if ($shippingCharge == null ){
            session()->flash('error', 'Shipping Not Found');
            return response()->json([
                'status' => true,
            ]);
        }

        $shippingCharge->delete();

        session()->flash('success', 'Shipping deleted successfully');
        return response()->json([
            'status' => true,
        ]);
    }
}
